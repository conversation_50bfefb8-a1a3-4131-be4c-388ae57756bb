import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { glob } from 'glob';
import fs from 'fs';
import path from 'path';
import { FileDiscoveryService } from '../services/fileDiscoveryService.js';

// Subset of 'Path' interface provided by 'glob' that we can implement for testing
export interface GlobPath {
  fullpath(): string;
  mtimeMs?: number;
}

/**
 * Sorts file entries based on recency and then alphabetically.
 * Recent files (modified within recencyThresholdMs) are listed first, newest to oldest.
 * Older files are listed after recent ones, sorted alphabetically by path.
 */
export function sortFileEntries(
  entries: GlobPath[],
  nowTimestamp: number,
  recencyThresholdMs: number,
): GlobPath[] {
  const sortedEntries = [...entries];
  sortedEntries.sort((a, b) => {
    const mtimeA = a.mtimeMs ?? 0;
    const mtimeB = b.mtimeMs ?? 0;
    const aIsRecent = nowTimestamp - mtimeA < recencyThresholdMs;
    const bIsRecent = nowTimestamp - mtimeB < recencyThresholdMs;

    if (aIsRecent && bIsRecent) {
      return mtimeB - mtimeA;
    } else if (aIsRecent) {
      return -1;
    } else if (bIsRecent) {
      return 1;
    } else {
      return a.fullpath().localeCompare(b.fullpath());
    }
  });
  return sortedEntries;
}

/**
 * Checks if a path is within a given root directory.
 * @param pathToCheck The absolute path to check.
 * @param rootDirectory The absolute root directory.
 * @returns True if the path is within the root directory, false otherwise.
 */
export function isWithinRoot(
  pathToCheck: string,
  rootDirectory: string,
): boolean {
  const normalizedPathToCheck = path.resolve(pathToCheck);
  const normalizedRootDirectory = path.resolve(rootDirectory);

  // Ensure the rootDirectory path ends with a separator for correct startsWith comparison,
  // unless it's the root path itself (e.g., '/' or 'C:\').
  const rootWithSeparator =
    normalizedRootDirectory === path.sep ||
    normalizedRootDirectory.endsWith(path.sep)
      ? normalizedRootDirectory
      : normalizedRootDirectory + path.sep;

  return (
    normalizedPathToCheck === normalizedRootDirectory ||
    normalizedPathToCheck.startsWith(rootWithSeparator)
  );
}

/**
 * Calculates the relative path from a root directory to a target path.
 * Ensures both paths are resolved before calculating.
 * Returns '.' if the target path is the same as the root directory.
 *
 * @param targetPath The absolute or relative path to make relative.
 * @param rootDirectory The absolute path of the directory to make the target path relative to.
 * @returns The relative path from rootDirectory to targetPath.
 */
export function makeRelative(
  targetPath: string,
  rootDirectory: string,
): string {
  const resolvedTargetPath = path.resolve(targetPath);
  const resolvedRootDirectory = path.resolve(rootDirectory);

  const relativePath = path.relative(resolvedRootDirectory, resolvedTargetPath);

  // If the paths are the same, path.relative returns '', return '.' instead
  return relativePath || '.';
}

/**
 * Shortens a file path for display purposes.
 * @param filePath The file path to shorten.
 * @param maxLen Maximum length of the shortened path.
 * @returns The shortened path.
 */
export function shortenPath(filePath: string, maxLen: number = 35): string {
  if (filePath.length <= maxLen) {
    return filePath;
  }

  const parsedPath = path.parse(filePath);
  const root = parsedPath.root;
  const separator = path.sep;

  // Get segments of the path *after* the root
  const relativePath = filePath.substring(root.length);
  const segments = relativePath.split(separator).filter((s) => s !== ''); // Filter out empty segments

  // Handle cases with no segments after root (e.g., "/", "C:\") or only one segment
  if (segments.length <= 1) {
    // Fall back to simple start/end truncation for very short paths or single segments
    const keepLen = Math.floor((maxLen - 3) / 2);
    // Ensure keepLen is not negative if maxLen is very small
    if (keepLen <= 0) {
      return filePath.substring(0, maxLen - 3) + '...';
    }
    const start = filePath.substring(0, keepLen);
    const end = filePath.substring(filePath.length - keepLen);
    return `${start}...${end}`;
  }

  const firstDir = segments[0];
  const lastSegment = segments[segments.length - 1];
  const startComponent = root + firstDir;

  const endPartSegments: string[] = [];
  // Base length: separator + "..." + lastDir
  let currentLength = separator.length + lastSegment.length;

  // Iterate backwards through segments (excluding the first one)
  for (let i = segments.length - 2; i >= 0; i--) {
    const segment = segments[i];
    // Length needed if we add this segment: current + separator + segment
    const lengthWithSegment = currentLength + separator.length + segment.length;

    if (lengthWithSegment <= maxLen) {
      endPartSegments.unshift(segment); // Add to the beginning of the end part
      currentLength = lengthWithSegment;
    } else {
      break;
    }
  }

  let result = endPartSegments.join(separator) + separator + lastSegment;

  if (currentLength > maxLen) {
    return result;
  }

  // Construct the final path
  result = startComponent + separator + result;

  // As a final check, if the result is somehow still too long
  // truncate the result string from the beginning, prefixing with "...".
  if (result.length > maxLen) {
    return '...' + result.substring(result.length - maxLen - 3);
  }

  return result;
}



/**
 * Parameters for the GlobTool
 */
export interface GlobToolParams {
  /**
   * The glob pattern to match files against
   */
  pattern: string;

  /**
   * The directory to search in (optional, defaults to current directory)
   */
  path?: string | null;

  /**
   * Whether the search should be case-sensitive (optional, defaults to false)
   */
  case_sensitive?: boolean | null;

  /**
   * Whether to respect .gitignore patterns (optional, defaults to true)
   */
  respect_git_ignore?: boolean | null;
}

/**
 * Validates the parameters for the glob tool.
 */
function validateGlobParams(params: GlobToolParams, targetDir: string): string | null {
  const searchDirAbsolute = path.resolve(
    targetDir,
    params.path || '.',
  );

  if (!isWithinRoot(searchDirAbsolute, targetDir)) {
    return `Search path ("${searchDirAbsolute}") resolves outside the tool's root directory ("${targetDir}").`;
  }

  try {
    if (!fs.existsSync(searchDirAbsolute)) {
      return `Search path does not exist ${searchDirAbsolute}`;
    }
    if (!fs.statSync(searchDirAbsolute).isDirectory()) {
      return `Search path is not a directory: ${searchDirAbsolute}`;
    }
  } catch (e: unknown) {
    return `Error accessing search path: ${e}`;
  }

  if (
    !params.pattern ||
    typeof params.pattern !== 'string' ||
    params.pattern.trim() === ''
  ) {
    return "The 'pattern' parameter cannot be empty.";
  }

  return null;
}



/**
 * Glob tool for langchain - finds files matching specific glob patterns
 * Implements the same core logic as packages/core/src/tools/glob.ts but follows langchainjs tool conventions
 */
export const globTool = tool(
  async (params: GlobToolParams) => {
    const targetDir = process.cwd();
    const validationError = validateGlobParams(params, targetDir);
    if (validationError) {
      throw new Error(`Invalid parameters: ${validationError}`);
    }

    try {
      const searchDirAbsolute = path.resolve(
        targetDir,
        params.path || '.',
      );

      // Get centralized file discovery service
      const respectGitIgnore = params.respect_git_ignore ?? true;
      const fileDiscovery = new FileDiscoveryService(targetDir);

      const entries = (await glob(params.pattern, {
        cwd: searchDirAbsolute,
        withFileTypes: true,
        nodir: true,
        stat: true,
        nocase: !params.case_sensitive,
        dot: true,
        ignore: ['**/node_modules/**', '**/.git/**'],
        follow: false,
      })) as GlobPath[];

      // Apply git-aware filtering if enabled and in git repository
      let filteredEntries = entries;
      let gitIgnoredCount = 0;

      if (respectGitIgnore) {
        const relativePaths = entries.map((p) =>
          path.relative(targetDir, p.fullpath()),
        );
        const filteredRelativePaths = fileDiscovery.filterFiles(relativePaths, {
          respectGitIgnore,
        });
        const filteredAbsolutePaths = new Set(
          filteredRelativePaths.map((p: string) =>
            path.resolve(targetDir, p),
          ),
        );

        filteredEntries = entries.filter((entry) =>
          filteredAbsolutePaths.has(entry.fullpath()),
        );
        gitIgnoredCount = entries.length - filteredEntries.length;
      }

      if (!filteredEntries || filteredEntries.length === 0) {
        let message = `No files found matching pattern "${params.pattern}" within ${searchDirAbsolute}.`;
        if (gitIgnoredCount > 0) {
          message += ` (${gitIgnoredCount} files were git-ignored)`;
        }
        return message;
      }

      // Set filtering such that we first show the most recent files
      const oneDayInMs = 24 * 60 * 60 * 1000;
      const nowTimestamp = new Date().getTime();

      // Sort the filtered entries using the new helper function
      const sortedEntries = sortFileEntries(
        filteredEntries,
        nowTimestamp,
        oneDayInMs,
      );

      const sortedAbsolutePaths = sortedEntries.map((entry) =>
        entry.fullpath(),
      );
      const fileListDescription = sortedAbsolutePaths.join('\n');
      const fileCount = sortedAbsolutePaths.length;

      let resultMessage = `Found ${fileCount} file(s) matching "${params.pattern}" within ${searchDirAbsolute}`;
      if (gitIgnoredCount > 0) {
        resultMessage += ` (${gitIgnoredCount} additional files were git-ignored)`;
      }
      resultMessage += `, sorted by modification time (newest first):\n${fileListDescription}`;

      return resultMessage;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`GlobTool execute Error: ${errorMessage}`, error);
      throw new Error(`Error during glob search operation: ${errorMessage}`);
    }
  },
  {
    name: "glob",
    description: "Efficiently finds files matching specific glob patterns (e.g., `src/**/*.ts`, `**/*.md`), returning absolute paths sorted by modification time (newest first). Ideal for quickly locating files based on their name or path structure, especially in large codebases.",
    schema: z.object({
      pattern: z.string().describe(
        "The glob pattern to match against (e.g., '**/*.py', 'docs/*.md')."
      ),
      path: z.string().nullable().optional().describe(
        'Optional: The absolute path to the directory to search within. If omitted, searches the root directory.'
      ),
      case_sensitive: z.boolean().nullable().optional().describe(
        'Optional: Whether the search should be case-sensitive. Defaults to false.'
      ),
      respect_git_ignore: z.boolean().nullable().optional().describe(
        'Optional: Whether to respect .gitignore patterns when finding files. Only available in git repositories. Defaults to true.'
      ),
    }),
  }
);
